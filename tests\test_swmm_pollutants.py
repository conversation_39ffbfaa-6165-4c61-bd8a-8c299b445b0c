import os
import unittest
import shutil
import pandas as pd
import numpy as np
import swmmio
from pyswmm import Simulation, Output
from swmmio.utils.modify_model import replace_inp_section

from tests.test_helper import TestHelper
from tests.data import TESTS_DATA_PATH, TESTS_OUTPUT_PATH

class TestSwmmPollutants(unittest.TestCase):

    def setUp(self):
        self.output_folder = os.path.join(TESTS_OUTPUT_PATH, 'swmm_pollutants')
        os.makedirs(self.output_folder, exist_ok=True)
        self.helper = TestHelper()

        # Define pollutants data for use in tests
        self.pollutants_data = {
            'Name': ['TSS', 'Lead', 'Zinc', 'Oil'],
            'Units': ['MG/L', 'UG/L', 'UG/L', 'MG/L'],
            'Crain': [0, 0, 0, 0],
            'Cgw': [0, 0, 0, 0],
            'Kdecay': [0.05, 0.02, 0.03, 0.1],
            'SnowOnly': [0, 0, 0, 0],  # Use 0 instead of 'NO'
            'CoPollutant': ['*', '*', '*', '*'],
            'CoFrac': [0, 0, 0, 0],
            'Cdwf': [0, 0, 0, 0],
            'Cinit': [0, 0, 0, 0]
        }
    
    def test_add_road_pollutants(self):
        # Find or copy a simple SWMM model to work with
        swmm_file = os.path.join(TESTS_DATA_PATH, 'demo_case1', 'demo_case_direct_inflow.inp')
        test_swmm_file = os.path.join(self.output_folder, 'road_pollutants_test.inp')
        
        # Copy the file to our test output directory
        shutil.copy2(swmm_file, test_swmm_file)
        
        # Load the model using swmmio and modify it directly
        model = swmmio.Model(test_swmm_file)

        # Add landuse to subcatchments first
        subcatchments = model.inp.subcatchments.copy()
        if 'Landuse' not in subcatchments.columns:
            subcatchments['Landuse'] = 'Residential'
        model.inp.subcatchments = subcatchments

        # Save the model with landuse added to a temporary file
        temp_file = test_swmm_file.replace('.inp', '_temp.inp')
        model.inp.save(temp_file)

        # Now manually add pollutant sections to the file
        self._add_pollutant_sections_to_file(temp_file, test_swmm_file)

        # Clean up temporary file
        if os.path.exists(temp_file):
            os.remove(temp_file)
        
        # Run the simulation
        output_file = os.path.join(self.output_folder, 'road_pollutants_results.out')
        report_file = os.path.join(self.output_folder, 'road_pollutants_report.rpt')
        
        # Change to output directory to run simulation
        original_dir = os.getcwd()
        os.chdir(self.output_folder)
        
        try:
            with Simulation(os.path.basename(test_swmm_file)) as sim:
                for step in sim:
                    pass
            
            # Check if output file was created
            self.assertTrue(os.path.exists(output_file), "Output file was not created")
            
            # Analyze results using pyswmm Output
            with Output(output_file) as out:
                # Get outfall nodes
                outfalls = [node for node in model.inp.outfalls.index]
                
                for outfall in outfalls:
                    print(f"Pollutant concentrations at outfall {outfall}:")
                    
                    # Get pollutant results for this node
                    for pollutant in self.pollutants_data['Name']:
                        try:
                            # Get pollutant concentration time series
                            pollutant_index = out.pollutants.index(pollutant)
                            conc_series = []
                            
                            for time_idx in range(out.num_periods):
                                conc = out.node_pollutant_quality(outfall, pollutant_index, time_idx)
                                conc_series.append(conc)
                            
                            # Convert to numpy array for calculations
                            conc_array = np.array(conc_series)
                            
                            # Print results
                            print(f"  {pollutant}: Max={np.max(conc_array)}, Avg={np.mean(conc_array)}")
                            
                            # Assert that concentrations are within expected ranges
                            self.assertGreaterEqual(np.max(conc_array), 0, 
                                                  f"Maximum {pollutant} concentration should be non-negative")
                        except Exception as e:
                            print(f"Error getting data for {pollutant}: {str(e)}")
        
        finally:
            # Change back to original directory
            os.chdir(original_dir)

    def _add_pollutant_sections_to_file(self, temp_file, output_file):
        """Helper method to add pollutant sections to SWMM file"""

        # Read the temporary file
        with open(temp_file, 'r') as f:
            lines = f.readlines()

        # Find where to insert LANDUSES section (before SUBCATCHMENTS)
        subcatchments_index = -1
        for i, line in enumerate(lines):
            if line.strip() == '[SUBCATCHMENTS]':
                subcatchments_index = i
                break

        if subcatchments_index == -1:
            raise ValueError("Could not find [SUBCATCHMENTS] section in the file")

        # Prepare sections to insert
        landuses_section = []
        landuses_section.append('\n[LANDUSES]\n')
        landuses_section.append(';;Name           Sweeping_Interval Fraction_Available Last_Swept\n')
        landuses_section.append(';;-------------- ----------------- ------------------ -----------\n')
        landuses_section.append('Residential      0                 0                  0\n')
        landuses_section.append('\n')

        # Insert LANDUSES section before SUBCATCHMENTS
        new_lines = lines[:subcatchments_index] + landuses_section + lines[subcatchments_index:]

        # Prepare pollutant sections to add at the end
        pollutant_sections = []

        # Add POLLUTANTS section
        pollutant_sections.append('\n\n[POLLUTANTS]\n')
        pollutant_sections.append(';;Name           Units  Crain Cgw   Kdecay SnowOnly CoPollutant CoFrac Cdwf Cinit\n')
        pollutant_sections.append(';;-------------- ------ ----- ---- ------ -------- ----------- ------ ---- -----\n')
        for name, units, crain, cgw, kdecay, snow_only, co_pollutant, co_frac, cdwf, cinit in zip(
            self.pollutants_data['Name'], self.pollutants_data['Units'], self.pollutants_data['Crain'],
            self.pollutants_data['Cgw'], self.pollutants_data['Kdecay'], self.pollutants_data['SnowOnly'],
            self.pollutants_data['CoPollutant'], self.pollutants_data['CoFrac'],
            self.pollutants_data['Cdwf'], self.pollutants_data['Cinit']
        ):
            pollutant_sections.append(f'{name:<16} {units:<6} {crain:<5} {cgw:<4} {kdecay:<6} {snow_only:<8} {co_pollutant:<11} {co_frac:<6} {cdwf:<4} {cinit}\n')

        # Add BUILDUP section
        pollutant_sections.append('\n\n[BUILDUP]\n')
        pollutant_sections.append(';;LandUse        Pollutant Function C1   C2  C3 PerArea\n')
        pollutant_sections.append(';;-------------- --------- -------- ---- --- -- -------\n')
        for pollutant in self.pollutants_data['Name']:
            pollutant_sections.append(f'Residential      {pollutant:<9} EXP      100  0.5 0  AREA\n')

        # Add WASHOFF section
        pollutant_sections.append('\n\n[WASHOFF]\n')
        pollutant_sections.append(';;LandUse        Pollutant Function C1  C2  C3 C4\n')
        pollutant_sections.append(';;-------------- --------- -------- --- --- -- --\n')
        for pollutant in self.pollutants_data['Name']:
            pollutant_sections.append(f'Residential      {pollutant:<9} EXP      0.1 1.2 0  0\n')

        # Write the modified file
        with open(output_file, 'w') as f:
            f.writelines(new_lines)
            f.writelines(pollutant_sections)

if __name__ == '__main__':
    unittest.main()
